/**
 * Firebase Cloud Messaging Service Worker
 * 
 * This service worker handles background notifications for the web app.
 * It must be placed in the public directory and served from the root.
 */

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js');

// Initialize Firebase in the service worker
const firebaseConfig = {
  apiKey: "AIzaSyCm1BDfCzYqqoRbmPBGEoH02LoRgKWcHWg",
  authDomain: "encreasl-daa43.firebaseapp.com",
  projectId: "encreasl-daa43",
  storageBucket: "encreasl-daa43.firebasestorage.app",
  messagingSenderId: "204287705323",
  appId: "1:204287705323:web:311b754f5618607515beae"
};

firebase.initializeApp(firebaseConfig);

// Get messaging instance
const messaging = firebase.messaging();

// ========================================
// BACKGROUND MESSAGE HANDLER
// ========================================

messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message:', payload);

  // Extract notification data
  const notificationTitle = payload.notification?.title || 'Encreasl Notification';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new notification',
    icon: payload.notification?.icon || '/icons/icon-192x192.png',
    badge: payload.notification?.badge || '/icons/badge-72x72.png',
    image: payload.notification?.image,
    tag: payload.notification?.tag || 'encreasl-notification',
    data: {
      ...payload.data,
      clickAction: payload.notification?.clickAction || payload.fcmOptions?.link || '/',
      timestamp: Date.now(),
    },
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/icons/view-icon.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/dismiss-icon.png'
      }
    ],
    requireInteraction: payload.data?.requireInteraction === 'true',
    silent: payload.data?.silent === 'true',
    vibrate: payload.data?.vibrate ? JSON.parse(payload.data.vibrate) : [200, 100, 200],
    renotify: payload.data?.renotify === 'true',
    timestamp: Date.now(),
  };

  // Show notification
  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// ========================================
// NOTIFICATION CLICK HANDLER
// ========================================

self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification clicked:', event);

  const notification = event.notification;
  const action = event.action;
  const data = notification.data || {};

  // Close the notification
  notification.close();

  // Handle different actions
  if (action === 'dismiss') {
    // Just close the notification
    console.log('Notification dismissed');
    
    // Track dismissal analytics
    trackNotificationEvent('dismissed', data);
    return;
  }

  // Default action or 'view' action
  const clickAction = data.clickAction || '/';
  
  // Track click analytics
  trackNotificationEvent('clicked', data);

  // Open or focus the app
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      const targetUrl = new URL(clickAction, self.location.origin).href;
      
      for (const client of clientList) {
        if (client.url === targetUrl && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no existing window/tab, open a new one
      if (clients.openWindow) {
        return clients.openWindow(clickAction);
      }
    })
  );
});

// ========================================
// NOTIFICATION CLOSE HANDLER
// ========================================

self.addEventListener('notificationclose', (event) => {
  console.log('[firebase-messaging-sw.js] Notification closed:', event);
  
  const data = event.notification.data || {};
  
  // Track close analytics
  trackNotificationEvent('closed', data);
});

// ========================================
// PUSH EVENT HANDLER (Fallback)
// ========================================

self.addEventListener('push', (event) => {
  console.log('[firebase-messaging-sw.js] Push event received:', event);

  if (!event.data) {
    console.log('Push event has no data');
    return;
  }

  try {
    const payload = event.data.json();
    console.log('Push payload:', payload);

    // If Firebase messaging didn't handle it, handle it manually
    if (payload.notification) {
      const notificationTitle = payload.notification.title || 'Encreasl Notification';
      const notificationOptions = {
        body: payload.notification.body || 'You have a new notification',
        icon: payload.notification.icon || '/icons/icon-192x192.png',
        badge: payload.notification.badge || '/icons/badge-72x72.png',
        image: payload.notification.image,
        tag: payload.notification.tag || 'encreasl-notification',
        data: payload.data || {},
        vibrate: [200, 100, 200],
        timestamp: Date.now(),
      };

      event.waitUntil(
        self.registration.showNotification(notificationTitle, notificationOptions)
      );
    }
  } catch (error) {
    console.error('Error parsing push payload:', error);
  }
});

// ========================================
// ANALYTICS TRACKING
// ========================================

function trackNotificationEvent(eventType, data) {
  try {
    // Send analytics to your backend
    const analyticsData = {
      event: eventType,
      timestamp: Date.now(),
      notificationId: data.notificationId,
      campaignId: data.campaignId,
      userId: data.userId,
      type: data.type,
      userAgent: navigator.userAgent,
    };

    // Use fetch to send analytics (if online)
    if (navigator.onLine) {
      fetch('/api/analytics/notification-events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(analyticsData),
      }).catch(error => {
        console.error('Failed to send notification analytics:', error);
      });
    } else {
      // Store for later if offline
      storeOfflineAnalytics(analyticsData);
    }
  } catch (error) {
    console.error('Error tracking notification event:', error);
  }
}

function storeOfflineAnalytics(data) {
  // Store analytics data in IndexedDB for later sync
  if ('indexedDB' in self) {
    const request = indexedDB.open('EncreaslAnalytics', 1);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('notificationEvents')) {
        db.createObjectStore('notificationEvents', { keyPath: 'id', autoIncrement: true });
      }
    };
    
    request.onsuccess = (event) => {
      const db = event.target.result;
      const transaction = db.transaction(['notificationEvents'], 'readwrite');
      const store = transaction.objectStore('notificationEvents');
      store.add(data);
    };
  }
}

// ========================================
// SYNC OFFLINE ANALYTICS
// ========================================

self.addEventListener('online', () => {
  console.log('Service worker is online, syncing analytics...');
  syncOfflineAnalytics();
});

function syncOfflineAnalytics() {
  if ('indexedDB' in self) {
    const request = indexedDB.open('EncreaslAnalytics', 1);
    
    request.onsuccess = (event) => {
      const db = event.target.result;
      const transaction = db.transaction(['notificationEvents'], 'readwrite');
      const store = transaction.objectStore('notificationEvents');
      const getAllRequest = store.getAll();
      
      getAllRequest.onsuccess = () => {
        const events = getAllRequest.result;
        
        events.forEach(event => {
          fetch('/api/analytics/notification-events', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(event),
          }).then(() => {
            // Remove synced event
            store.delete(event.id);
          }).catch(error => {
            console.error('Failed to sync notification analytics:', error);
          });
        });
      };
    };
  }
}

// ========================================
// SERVICE WORKER LIFECYCLE
// ========================================

self.addEventListener('install', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker installing...');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker activating...');
  event.waitUntil(self.clients.claim());
});

// ========================================
// ERROR HANDLING
// ========================================

self.addEventListener('error', (event) => {
  console.error('[firebase-messaging-sw.js] Service worker error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('[firebase-messaging-sw.js] Unhandled promise rejection:', event.reason);
});
