import {
  doc,
  collection,
  addDoc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  onSnapshot,
  serverTimestamp,
  increment,
  arrayUnion,
  arrayRemove,
  writeBatch,
  runTransaction,
  DocumentData,
  DocumentReference,
  CollectionReference,
  QuerySnapshot,
  DocumentSnapshot,
  Timestamp,
  FieldValue,
  Unsubscribe,
} from 'firebase/firestore';
import { firebaseDb } from './firebase';

// Base document interface
export interface BaseDocument {
  id?: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Contact form submission
export interface ContactSubmission extends BaseDocument {
  name: string;
  email: string;
  company?: string;
  message: string;
  status: 'new' | 'in-progress' | 'resolved';
  source: 'website' | 'landing-page';
}

// Newsletter subscription
export interface NewsletterSubscription extends BaseDocument {
  email: string;
  status: 'active' | 'unsubscribed';
  source: string;
  preferences?: {
    marketing: boolean;
    updates: boolean;
    newsletter: boolean;
  };
}

// Blog post
export interface BlogPost extends BaseDocument {
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  author: string;
  tags: string[];
  published: boolean;
  publishedAt?: Timestamp;
  featuredImage?: string;
  seoTitle?: string;
  seoDescription?: string;
}

// Generic CRUD operations
export class FirestoreService<T extends BaseDocument> {
  private collectionRef: CollectionReference<DocumentData>;

  constructor(collectionName: string) {
    this.collectionRef = collection(firebaseDb, collectionName);
  }

  // Create a new document
  async create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const docData = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };
      
      const docRef = await addDoc(this.collectionRef, docData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating document:', error);
      throw new Error('Failed to create document');
    }
  }

  // Create with custom ID
  async createWithId(id: string, data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {
    try {
      const docData = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };
      
      const docRef = doc(this.collectionRef, id);
      await setDoc(docRef, docData);
    } catch (error) {
      console.error('Error creating document with ID:', error);
      throw new Error('Failed to create document');
    }
  }

  // Get a document by ID
  async getById(id: string): Promise<T | null> {
    try {
      const docRef = doc(this.collectionRef, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as T;
      }
      return null;
    } catch (error) {
      console.error('Error getting document:', error);
      throw new Error('Failed to get document');
    }
  }

  // Get all documents
  async getAll(): Promise<T[]> {
    try {
      const querySnapshot = await getDocs(this.collectionRef);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as T));
    } catch (error) {
      console.error('Error getting documents:', error);
      throw new Error('Failed to get documents');
    }
  }

  // Update a document
  async update(id: string, data: Partial<Omit<T, 'id' | 'createdAt'>>): Promise<void> {
    try {
      const docRef = doc(this.collectionRef, id);
      const updateData = {
        ...data,
        updatedAt: serverTimestamp(),
      };
      
      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Error updating document:', error);
      throw new Error('Failed to update document');
    }
  }

  // Delete a document
  async delete(id: string): Promise<void> {
    try {
      const docRef = doc(this.collectionRef, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting document:', error);
      throw new Error('Failed to delete document');
    }
  }

  // Query documents
  async query(
    constraints: any[] = [],
    limitCount?: number
  ): Promise<T[]> {
    try {
      let q = query(this.collectionRef, ...constraints);
      
      if (limitCount) {
        q = query(q, limit(limitCount));
      }
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as T));
    } catch (error) {
      console.error('Error querying documents:', error);
      throw new Error('Failed to query documents');
    }
  }

  // Listen to real-time updates
  onSnapshot(
    callback: (documents: T[]) => void,
    constraints: any[] = []
  ): Unsubscribe {
    const q = query(this.collectionRef, ...constraints);
    
    return onSnapshot(q, (querySnapshot) => {
      const documents = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as T));
      callback(documents);
    });
  }
}

// Service instances
export const contactService = new FirestoreService<ContactSubmission>('contacts');
export const newsletterService = new FirestoreService<NewsletterSubscription>('newsletter');
export const blogService = new FirestoreService<BlogPost>('blog');

// Specific helper functions
export async function submitContactForm(data: {
  name: string;
  email: string;
  company?: string;
  message: string;
  source?: string;
}): Promise<string> {
  return await contactService.create({
    ...data,
    status: 'new',
    source: data.source as 'website' || 'website',
  });
}

export async function subscribeToNewsletter(email: string, source = 'website'): Promise<string> {
  return await newsletterService.create({
    email,
    status: 'active',
    source,
    preferences: {
      marketing: true,
      updates: true,
      newsletter: true,
    },
  });
}

export async function getPublishedBlogPosts(): Promise<BlogPost[]> {
  return await blogService.query([
    where('published', '==', true),
    orderBy('publishedAt', 'desc')
  ]);
}

export async function getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
  const posts = await blogService.query([
    where('slug', '==', slug),
    where('published', '==', true),
    limit(1)
  ]);
  
  return posts.length > 0 ? posts[0] : null;
}

// Batch operations
export async function batchUpdate<T extends BaseDocument>(
  service: FirestoreService<T>,
  updates: Array<{ id: string; data: Partial<T> }>
): Promise<void> {
  const batch = writeBatch(firebaseDb);
  
  updates.forEach(({ id, data }) => {
    const docRef = doc(firebaseDb, service['collectionRef'].path, id);
    batch.update(docRef, { ...data, updatedAt: serverTimestamp() });
  });
  
  await batch.commit();
}

// Transaction example
export async function transferData<T extends BaseDocument>(
  fromService: FirestoreService<T>,
  toService: FirestoreService<T>,
  fromId: string,
  toData: Omit<T, 'id' | 'createdAt' | 'updatedAt'>
): Promise<string> {
  return await runTransaction(firebaseDb, async (transaction) => {
    // Get source document
    const fromRef = doc(firebaseDb, fromService['collectionRef'].path, fromId);
    const fromDoc = await transaction.get(fromRef);
    
    if (!fromDoc.exists()) {
      throw new Error('Source document does not exist');
    }
    
    // Create new document
    const toRef = doc(toService['collectionRef']);
    transaction.set(toRef, {
      ...toData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    
    // Delete source document
    transaction.delete(fromRef);
    
    return toRef.id;
  });
}

// Export types
export type {
  DocumentData,
  DocumentReference,
  CollectionReference,
  QuerySnapshot,
  DocumentSnapshot,
  Timestamp,
  FieldValue,
  Unsubscribe,
};
