import { NextRequest, NextResponse } from 'next/server';
import { getAdminDb } from '@/lib/firebase-admin';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { name, email, company, message, source = 'website' } = body;

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: name, email, message' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Get Firebase Admin Firestore instance
    const db = getAdminDb();

    // Create contact submission
    const contactData = {
      name: name.trim(),
      email: email.toLowerCase().trim(),
      company: company?.trim() || null,
      message: message.trim(),
      status: 'new',
      source,
      priority: 'medium',
      tags: [],
      notes: [],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    // Add to Firestore
    const docRef = await addDoc(collection(db, 'contacts'), contactData);

    // Log the submission (optional)
    console.log('Contact form submitted:', {
      id: docRef.id,
      email,
      source,
      timestamp: new Date().toISOString(),
    });

    // Send notification email (you would implement this)
    // await sendNotificationEmail(contactData);

    return NextResponse.json(
      { 
        success: true, 
        id: docRef.id,
        message: 'Contact form submitted successfully' 
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Contact form submission error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to submit contact form' 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // This endpoint could be used to get contact statistics
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');

    // Get Firebase Admin Firestore instance
    const db = getAdminDb();

    // Build query
    let query = collection(db, 'contacts');
    
    // You would add query constraints here based on parameters
    // This is a simplified example

    return NextResponse.json({
      message: 'Contact API endpoint',
      availableEndpoints: {
        POST: 'Submit a new contact form',
        GET: 'Get contact statistics (admin only)',
      }
    });

  } catch (error) {
    console.error('Contact API error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
