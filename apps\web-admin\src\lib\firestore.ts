import {
  doc,
  collection,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  limit,
  startAfter,
  serverTimestamp,
  writeBatch,
  DocumentData,
  DocumentReference,
  CollectionReference,
  QuerySnapshot,
  DocumentSnapshot,
  Timestamp,
  FieldValue,
  Unsubscribe,
  QueryConstraint,
} from 'firebase/firestore';
import { firebaseDb } from './firebase';

// Base document interface
export interface BaseDocument {
  id?: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Admin user management
export interface AdminUser extends BaseDocument {
  uid: string;
  email: string;
  displayName: string;
  role: 'super-admin' | 'admin' | 'editor' | 'viewer';
  permissions: string[];
  isActive: boolean;
  lastLogin?: Timestamp;
  department?: string;
  notes?: string;
}

// Contact form submission (admin view)
export interface ContactSubmission extends BaseDocument {
  name: string;
  email: string;
  company?: string;
  message: string;
  status: 'new' | 'in-progress' | 'resolved' | 'spam';
  source: 'website' | 'landing-page' | 'referral';
  assignedTo?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tags: string[];
  notes: string[];
}

// Newsletter subscription (admin view)
export interface NewsletterSubscription extends BaseDocument {
  email: string;
  status: 'active' | 'unsubscribed' | 'bounced' | 'complained';
  source: string;
  preferences: {
    marketing: boolean;
    updates: boolean;
    newsletter: boolean;
  };
  segments: string[];
  lastEngagement?: Timestamp;
}

// Campaign management
export interface Campaign extends BaseDocument {
  name: string;
  type: 'email' | 'social' | 'ppc' | 'content';
  status: 'draft' | 'scheduled' | 'active' | 'paused' | 'completed';
  startDate: Timestamp;
  endDate?: Timestamp;
  budget?: number;
  spent?: number;
  targetAudience: string[];
  metrics: {
    impressions: number;
    clicks: number;
    conversions: number;
    revenue: number;
  };
  createdBy: string;
}

// Analytics data
export interface AnalyticsData extends BaseDocument {
  date: Timestamp;
  type: 'daily' | 'weekly' | 'monthly';
  metrics: {
    visitors: number;
    pageViews: number;
    bounceRate: number;
    avgSessionDuration: number;
    conversions: number;
    revenue: number;
  };
  sources: {
    organic: number;
    direct: number;
    social: number;
    email: number;
    paid: number;
  };
}

// Content management
export interface ContentItem extends BaseDocument {
  title: string;
  type: 'blog' | 'page' | 'case-study' | 'resource';
  slug: string;
  content: string;
  excerpt: string;
  author: string;
  status: 'draft' | 'review' | 'published' | 'archived';
  publishedAt?: Timestamp;
  featuredImage?: string;
  seoTitle?: string;
  seoDescription?: string;
  tags: string[];
  category: string;
  viewCount: number;
}

// Enhanced CRUD operations for admin
export class AdminFirestoreService<T extends BaseDocument> {
  private collectionRef: CollectionReference<DocumentData>;

  constructor(collectionName: string) {
    this.collectionRef = collection(firebaseDb, collectionName);
  }

  // Create with audit trail
  async create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>, createdBy?: string): Promise<string> {
    try {
      const docData = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        ...(createdBy && { createdBy }),
      };
      
      const docRef = await addDoc(this.collectionRef, docData);
      
      // Log admin action
      await this.logAdminAction('create', docRef.id, createdBy);
      
      return docRef.id;
    } catch (error) {
      console.error('Error creating document:', error);
      throw new Error('Failed to create document');
    }
  }

  // Get with pagination
  async getPaginated(
    pageSize = 20,
    lastDoc?: DocumentSnapshot,
    constraints: QueryConstraint[] = []
  ): Promise<{ documents: T[]; lastDoc: DocumentSnapshot | null; hasMore: boolean }> {
    try {
      let q = query(this.collectionRef, ...constraints, limit(pageSize + 1));
      
      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }
      
      const querySnapshot = await getDocs(q);
      const docs = querySnapshot.docs;
      const hasMore = docs.length > pageSize;
      
      const documents = docs.slice(0, pageSize).map(doc => ({
        id: doc.id,
        ...doc.data()
      } as T));
      
      const newLastDoc = hasMore ? docs[pageSize - 1] : null;
      
      return { documents, lastDoc: newLastDoc, hasMore };
    } catch (error) {
      console.error('Error getting paginated documents:', error);
      throw new Error('Failed to get documents');
    }
  }

  // Update with audit trail
  async update(id: string, data: Partial<T>, updatedBy?: string): Promise<void> {
    try {
      const docRef = doc(this.collectionRef, id);
      const updateData = {
        ...data,
        updatedAt: serverTimestamp(),
        ...(updatedBy && { updatedBy }),
      };

      await updateDoc(docRef, updateData);

      // Log admin action
      await this.logAdminAction('update', id, updatedBy);
    } catch (error) {
      console.error('Error updating document:', error);
      throw new Error('Failed to update document');
    }
  }

  // Advanced search
  async search(searchTerm: string, fields: string[]): Promise<T[]> {
    try {
      // This is a simplified search - in production, use Algolia or similar
      const promises = fields.map(field => 
        this.query([
          where(field, '>=', searchTerm),
          where(field, '<=', searchTerm + '\uf8ff')
        ])
      );
      
      const results = await Promise.all(promises);
      const allDocs = results.flat();
      
      // Remove duplicates
      const uniqueDocs = allDocs.filter((doc, index, self) => 
        index === self.findIndex(d => d.id === doc.id)
      );
      
      return uniqueDocs;
    } catch (error) {
      console.error('Error searching documents:', error);
      throw new Error('Failed to search documents');
    }
  }

  // Bulk operations
  async bulkUpdate(updates: Array<{ id: string; data: Partial<T> }>, updatedBy?: string): Promise<void> {
    try {
      const batch = writeBatch(firebaseDb);
      
      updates.forEach(({ id, data }) => {
        const docRef = doc(this.collectionRef, id);
        batch.update(docRef, {
          ...data,
          updatedAt: serverTimestamp(),
          ...(updatedBy && { updatedBy }),
        });
      });
      
      await batch.commit();
      
      // Log bulk action
      if (updatedBy) {
        await this.logAdminAction('bulk_update', `${updates.length} documents`, updatedBy);
      }
    } catch (error) {
      console.error('Error bulk updating documents:', error);
      throw new Error('Failed to bulk update documents');
    }
  }

  // Soft delete
  async softDelete(id: string, deletedBy?: string): Promise<void> {
    try {
      const docRef = doc(this.collectionRef, id);
      await updateDoc(docRef, {
        isDeleted: true,
        deletedAt: serverTimestamp(),
        deletedBy: deletedBy || 'unknown',
        updatedAt: serverTimestamp(),
      });
      
      // Log admin action
      await this.logAdminAction('soft_delete', id, deletedBy);
    } catch (error) {
      console.error('Error soft deleting document:', error);
      throw new Error('Failed to delete document');
    }
  }

  // Get analytics
  async getAnalytics(dateRange: { start: Date; end: Date }): Promise<{ count: number; documents: T[] }> {
    try {
      const startTimestamp = Timestamp.fromDate(dateRange.start);
      const endTimestamp = Timestamp.fromDate(dateRange.end);
      
      const q = query(
        this.collectionRef,
        where('createdAt', '>=', startTimestamp),
        where('createdAt', '<=', endTimestamp)
      );
      
      const querySnapshot = await getDocs(q);
      
      return {
        count: querySnapshot.size,
        documents: querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as T))
      };
    } catch (error) {
      console.error('Error getting analytics:', error);
      throw new Error('Failed to get analytics');
    }
  }

  // Log admin actions
  private async logAdminAction(action: string, targetId: string, userId?: string): Promise<void> {
    if (!userId) return;
    
    try {
      const auditRef = collection(firebaseDb, 'audit_logs');
      await addDoc(auditRef, {
        action,
        targetCollection: this.collectionRef.path,
        targetId,
        userId,
        timestamp: serverTimestamp(),
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
      });
    } catch (error) {
      console.error('Error logging admin action:', error);
      // Don't throw here to avoid breaking the main operation
    }
  }

  // Inherit base methods
  async getById(id: string): Promise<T | null> {
    try {
      const docRef = doc(this.collectionRef, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as T;
      }
      return null;
    } catch (error) {
      console.error('Error getting document:', error);
      throw new Error('Failed to get document');
    }
  }

  async query(constraints: QueryConstraint[] = [], limitCount?: number): Promise<T[]> {
    try {
      let q = query(this.collectionRef, ...constraints);
      
      if (limitCount) {
        q = query(q, limit(limitCount));
      }
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as T));
    } catch (error) {
      console.error('Error querying documents:', error);
      throw new Error('Failed to query documents');
    }
  }
}

// Admin service instances
export const adminUserService = new AdminFirestoreService<AdminUser>('admin_users');
export const contactAdminService = new AdminFirestoreService<ContactSubmission>('contacts');
export const newsletterAdminService = new AdminFirestoreService<NewsletterSubscription>('newsletter');
export const campaignService = new AdminFirestoreService<Campaign>('campaigns');
export const analyticsService = new AdminFirestoreService<AnalyticsData>('analytics');
export const contentService = new AdminFirestoreService<ContentItem>('content');

// Export types
export type {
  DocumentData,
  DocumentReference,
  CollectionReference,
  QuerySnapshot,
  DocumentSnapshot,
  Timestamp,
  FieldValue,
  Unsubscribe,
};
