/**
 * Firebase Cloud Messaging Service Worker for Admin App
 * 
 * This service worker handles background notifications for the admin app.
 * It includes enhanced features for admin-specific notifications.
 */

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js');

// Initialize Firebase in the service worker
const firebaseConfig = {
  apiKey: "AIzaSyCm1BDfCzYqqoRbmPBGEoH02LoRgKWcHWg",
  authDomain: "encreasl-daa43.firebaseapp.com",
  projectId: "encreasl-daa43",
  storageBucket: "encreasl-daa43.firebasestorage.app",
  messagingSenderId: "204287705323",
  appId: "1:204287705323:web:311b754f5618607515beae"
};

firebase.initializeApp(firebaseConfig);

// Get messaging instance
const messaging = firebase.messaging();

// ========================================
// BACKGROUND MESSAGE HANDLER
// ========================================

messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Admin received background message:', payload);

  // Extract notification data
  const notificationTitle = payload.notification?.title || 'Encreasl Admin Alert';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new admin notification',
    icon: payload.notification?.icon || '/icons/admin-icon-192x192.png',
    badge: payload.notification?.badge || '/icons/admin-badge-72x72.png',
    image: payload.notification?.image,
    tag: payload.notification?.tag || 'encreasl-admin-notification',
    data: {
      ...payload.data,
      clickAction: payload.notification?.clickAction || payload.fcmOptions?.link || '/dashboard',
      timestamp: Date.now(),
      isAdmin: true,
    },
    actions: getNotificationActions(payload.data?.type),
    requireInteraction: payload.data?.priority === 'urgent' || payload.data?.requireInteraction === 'true',
    silent: payload.data?.silent === 'true',
    vibrate: getVibrationPattern(payload.data?.priority),
    renotify: payload.data?.renotify === 'true',
    timestamp: Date.now(),
  };

  // Show notification
  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// ========================================
// ADMIN-SPECIFIC NOTIFICATION ACTIONS
// ========================================

function getNotificationActions(type) {
  const baseActions = [
    {
      action: 'view',
      title: 'View',
      icon: '/icons/view-icon.png'
    },
    {
      action: 'dismiss',
      title: 'Dismiss',
      icon: '/icons/dismiss-icon.png'
    }
  ];

  switch (type) {
    case 'contact_form':
      return [
        {
          action: 'respond',
          title: 'Respond',
          icon: '/icons/respond-icon.png'
        },
        {
          action: 'assign',
          title: 'Assign',
          icon: '/icons/assign-icon.png'
        },
        ...baseActions
      ];
    
    case 'system_alert':
      return [
        {
          action: 'investigate',
          title: 'Investigate',
          icon: '/icons/investigate-icon.png'
        },
        {
          action: 'acknowledge',
          title: 'Acknowledge',
          icon: '/icons/ack-icon.png'
        },
        ...baseActions
      ];
    
    case 'user_signup':
      return [
        {
          action: 'approve',
          title: 'Approve',
          icon: '/icons/approve-icon.png'
        },
        {
          action: 'review',
          title: 'Review',
          icon: '/icons/review-icon.png'
        },
        ...baseActions
      ];
    
    default:
      return baseActions;
  }
}

function getVibrationPattern(priority) {
  switch (priority) {
    case 'urgent':
      return [200, 100, 200, 100, 200, 100, 200];
    case 'high':
      return [300, 100, 300];
    case 'normal':
      return [200, 100, 200];
    case 'low':
      return [100];
    default:
      return [200, 100, 200];
  }
}

// ========================================
// NOTIFICATION CLICK HANDLER
// ========================================

self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Admin notification clicked:', event);

  const notification = event.notification;
  const action = event.action;
  const data = notification.data || {};

  // Close the notification
  notification.close();

  // Handle different actions
  switch (action) {
    case 'dismiss':
      trackNotificationEvent('dismissed', data);
      return;
    
    case 'respond':
      handleRespondAction(data);
      break;
    
    case 'assign':
      handleAssignAction(data);
      break;
    
    case 'investigate':
      handleInvestigateAction(data);
      break;
    
    case 'acknowledge':
      handleAcknowledgeAction(data);
      break;
    
    case 'approve':
      handleApproveAction(data);
      break;
    
    case 'review':
      handleReviewAction(data);
      break;
    
    default:
      // Default action or 'view' action
      handleViewAction(data);
      break;
  }
});

// ========================================
// ACTION HANDLERS
// ========================================

function handleViewAction(data) {
  const clickAction = data.clickAction || '/dashboard';
  trackNotificationEvent('clicked', data);
  openOrFocusWindow(clickAction);
}

function handleRespondAction(data) {
  trackNotificationEvent('respond_clicked', data);
  const respondUrl = `/contacts/${data.contactId || data.id}?action=respond`;
  openOrFocusWindow(respondUrl);
}

function handleAssignAction(data) {
  trackNotificationEvent('assign_clicked', data);
  const assignUrl = `/contacts/${data.contactId || data.id}?action=assign`;
  openOrFocusWindow(assignUrl);
}

function handleInvestigateAction(data) {
  trackNotificationEvent('investigate_clicked', data);
  const investigateUrl = `/system/alerts/${data.alertId || data.id}`;
  openOrFocusWindow(investigateUrl);
}

function handleAcknowledgeAction(data) {
  trackNotificationEvent('acknowledge_clicked', data);
  
  // Send acknowledgment to backend
  if (navigator.onLine) {
    fetch('/api/admin/alerts/acknowledge', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        alertId: data.alertId || data.id,
        timestamp: Date.now(),
      }),
    }).catch(error => {
      console.error('Failed to acknowledge alert:', error);
    });
  }
}

function handleApproveAction(data) {
  trackNotificationEvent('approve_clicked', data);
  
  // Send approval to backend
  if (navigator.onLine) {
    fetch('/api/admin/users/approve', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: data.userId || data.id,
        timestamp: Date.now(),
      }),
    }).catch(error => {
      console.error('Failed to approve user:', error);
    });
  }
}

function handleReviewAction(data) {
  trackNotificationEvent('review_clicked', data);
  const reviewUrl = `/users/${data.userId || data.id}?action=review`;
  openOrFocusWindow(reviewUrl);
}

function openOrFocusWindow(url) {
  clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
    const targetUrl = new URL(url, self.location.origin).href;
    
    for (const client of clientList) {
      if (client.url === targetUrl && 'focus' in client) {
        return client.focus();
      }
    }
    
    if (clients.openWindow) {
      return clients.openWindow(url);
    }
  });
}

// ========================================
// NOTIFICATION CLOSE HANDLER
// ========================================

self.addEventListener('notificationclose', (event) => {
  console.log('[firebase-messaging-sw.js] Admin notification closed:', event);
  
  const data = event.notification.data || {};
  trackNotificationEvent('closed', data);
});

// ========================================
// ANALYTICS TRACKING
// ========================================

function trackNotificationEvent(eventType, data) {
  try {
    const analyticsData = {
      event: eventType,
      timestamp: Date.now(),
      notificationId: data.notificationId,
      campaignId: data.campaignId,
      userId: data.userId,
      type: data.type,
      isAdmin: true,
      userAgent: navigator.userAgent,
    };

    if (navigator.onLine) {
      fetch('/api/admin/analytics/notification-events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(analyticsData),
      }).catch(error => {
        console.error('Failed to send admin notification analytics:', error);
      });
    } else {
      storeOfflineAnalytics(analyticsData);
    }
  } catch (error) {
    console.error('Error tracking admin notification event:', error);
  }
}

function storeOfflineAnalytics(data) {
  if ('indexedDB' in self) {
    const request = indexedDB.open('EncreaslAdminAnalytics', 1);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('notificationEvents')) {
        db.createObjectStore('notificationEvents', { keyPath: 'id', autoIncrement: true });
      }
    };
    
    request.onsuccess = (event) => {
      const db = event.target.result;
      const transaction = db.transaction(['notificationEvents'], 'readwrite');
      const store = transaction.objectStore('notificationEvents');
      store.add(data);
    };
  }
}

// ========================================
// SYNC OFFLINE ANALYTICS
// ========================================

self.addEventListener('online', () => {
  console.log('Admin service worker is online, syncing analytics...');
  syncOfflineAnalytics();
});

function syncOfflineAnalytics() {
  if ('indexedDB' in self) {
    const request = indexedDB.open('EncreaslAdminAnalytics', 1);
    
    request.onsuccess = (event) => {
      const db = event.target.result;
      const transaction = db.transaction(['notificationEvents'], 'readwrite');
      const store = transaction.objectStore('notificationEvents');
      const getAllRequest = store.getAll();
      
      getAllRequest.onsuccess = () => {
        const events = getAllRequest.result;
        
        events.forEach(event => {
          fetch('/api/admin/analytics/notification-events', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(event),
          }).then(() => {
            store.delete(event.id);
          }).catch(error => {
            console.error('Failed to sync admin notification analytics:', error);
          });
        });
      };
    };
  }
}

// ========================================
// SERVICE WORKER LIFECYCLE
// ========================================

self.addEventListener('install', (event) => {
  console.log('[firebase-messaging-sw.js] Admin service worker installing...');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('[firebase-messaging-sw.js] Admin service worker activating...');
  event.waitUntil(self.clients.claim());
});

// ========================================
// ERROR HANDLING
// ========================================

self.addEventListener('error', (event) => {
  console.error('[firebase-messaging-sw.js] Admin service worker error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('[firebase-messaging-sw.js] Admin unhandled promise rejection:', event.reason);
});
