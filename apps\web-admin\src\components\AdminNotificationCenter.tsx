'use client';

import { useState, useEffect } from 'react';
import { useAdminFCM, useAdminNotifications } from '@/hooks/useFCM';
import { useAuth } from '@/hooks/useAuth';

export function AdminNotificationCenter() {
  const { user, profile } = useAuth();
  const {
    isSupported,
    isInitialized,
    error,
    urgentMessageCount,
    unreadMessageCount,
    initialize,
    markMessageAsRead,
    clearUrgentMessages,
    clearError,
  } = useAdminFCM();

  const {
    notifications,
    loading: notificationsLoading,
    clearAllNotifications,
  } = useAdminNotifications();

  const [isOpen, setIsOpen] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);

  // Auto-initialize for admin users
  useEffect(() => {
    if (user && profile?.isAdmin && isSupported && !isInitialized && !isInitializing) {
      handleInitialize();
    }
  }, [user, profile?.isAdmin, isSupported, isInitialized]);

  const handleInitialize = async () => {
    setIsInitializing(true);
    try {
      await initialize();
    } catch (err) {
      console.error('Failed to initialize admin notifications:', err);
    } finally {
      setIsInitializing(false);
    }
  };

  const handleNotificationClick = (notification: { id: string; payload?: { fcmOptions?: { link?: string }; data?: { clickAction?: string } } }) => {
    const messageId = notification.id;
    markMessageAsRead(messageId);
    
    // Navigate to relevant page
    const clickAction = notification.payload?.fcmOptions?.link || 
                       notification.payload?.data?.clickAction || 
                       '/admin/dashboard';
    window.location.href = clickAction;
  };

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'contact_form':
        return '📧';
      case 'user_signup':
        return '👤';
      case 'system_alert':
        return '🚨';
      case 'campaign_update':
        return '📊';
      default:
        return '🔔';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'normal':
        return 'text-blue-600 bg-blue-100';
      case 'low':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-blue-600 bg-blue-100';
    }
  };

  if (!user || !profile?.isAdmin) {
    return null;
  }

  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full"
      >
        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-12" />
        </svg>
        
        {/* Notification Badges */}
        {urgentMessageCount > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
            {urgentMessageCount > 9 ? '9+' : urgentMessageCount}
          </span>
        )}
        {urgentMessageCount === 0 && unreadMessageCount > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center">
            {unreadMessageCount > 9 ? '9+' : unreadMessageCount}
          </span>
        )}
      </button>

      {/* Notification Panel */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                Admin Notifications
              </h3>
              <div className="flex items-center space-x-2">
                {urgentMessageCount > 0 && (
                  <button
                    onClick={clearUrgentMessages}
                    className="text-xs text-red-600 hover:text-red-800"
                  >
                    Clear Urgent
                  </button>
                )}
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Setup Prompt */}
          {!isInitialized && isSupported && (
            <div className="p-4 bg-yellow-50 border-b border-yellow-200">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-3 flex-1">
                  <h4 className="text-sm font-medium text-yellow-800">
                    Enable Admin Notifications
                  </h4>
                  <p className="mt-1 text-sm text-yellow-700">
                    Get real-time alerts for contact forms, system issues, and user activities.
                  </p>
                  <button
                    onClick={handleInitialize}
                    disabled={isInitializing}
                    className="mt-2 bg-yellow-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-yellow-700 disabled:opacity-50"
                  >
                    {isInitializing ? 'Enabling...' : 'Enable Now'}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="p-4 bg-red-50 border-b border-red-200">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm text-red-700">{error}</p>
                  <button
                    onClick={clearError}
                    className="mt-1 text-red-600 text-xs hover:text-red-800"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {notificationsLoading ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-500">Loading notifications...</p>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-8 text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-12" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
                <p className="mt-1 text-sm text-gray-500">
                  You're all caught up! New admin notifications will appear here.
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {notifications.slice(0, 10).map((notification) => (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className="p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 text-lg">
                        {getNotificationIcon(notification.payload?.data?.type || 'default')}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {notification.payload?.notification?.title || 'Admin Notification'}
                          </p>
                          <div className="flex items-center space-x-2">
                            {notification.payload?.data?.priority && (
                              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(notification.payload.data.priority)}`}>
                                {notification.payload.data.priority.toUpperCase()}
                              </span>
                            )}
                            <span className="text-xs text-gray-500">
                              {formatTimeAgo(notification.timestamp || Date.now())}
                            </span>
                          </div>
                        </div>
                        <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                          {notification.payload?.notification?.body || 'No description available'}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <button
                  onClick={clearAllNotifications}
                  className="text-sm text-red-600 hover:text-red-800"
                >
                  Clear All
                </button>
                <a
                  href="/admin/notifications"
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  View All Notifications
                </a>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
