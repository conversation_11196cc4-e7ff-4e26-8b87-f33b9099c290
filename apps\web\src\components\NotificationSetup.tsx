'use client';

import { useState, useEffect } from 'react';
import { useFCM, useNotificationPreferences } from '@/hooks/useFCM';
import { useAuth } from '@/hooks/useAuth';

export function NotificationSetup() {
  const { user } = useAuth();
  const {
    isSupported,
    isPermissionGranted,
    isInitialized,
    error,
    initialize,
    subscribeToTopic,
    unsubscribeFromTopic,
    clearError,
  } = useFCM();
  
  const {
    preferences,
    loading: prefsLoading,
    updatePreferences,
  } = useNotificationPreferences();

  const [isExpanded, setIsExpanded] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Show setup prompt if not initialized and user is logged in
  const shouldShowSetup = user && isSupported && !isInitialized && !isPermissionGranted;

  const handleEnableNotifications = async () => {
    setIsUpdating(true);
    try {
      const success = await initialize();
      if (success) {
        console.log('Notifications enabled successfully');
      }
    } catch (err) {
      console.error('Failed to enable notifications:', err);
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePreferenceChange = async (key: string, value: boolean) => {
    if (!preferences) return;

    setIsUpdating(true);
    try {
      const updatedPrefs = {
        ...preferences,
        preferences: {
          ...preferences.preferences,
          [key]: value,
        },
      };

      const success = await updatePreferences(updatedPrefs);
      if (success) {
        // Subscribe/unsubscribe from relevant topics
        if (key === 'marketing' && value) {
          await subscribeToTopic('marketing');
        } else if (key === 'marketing' && !value) {
          await unsubscribeFromTopic('marketing');
        }
      }
    } catch (err) {
      console.error('Failed to update preferences:', err);
    } finally {
      setIsUpdating(false);
    }
  };

  if (!user || !isSupported) {
    return null;
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
      {/* Setup Prompt */}
      {shouldShowSetup && (
        <div className="p-4 bg-blue-50 border-b border-blue-200">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-12" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-blue-800">
                Enable Notifications
              </h3>
              <p className="mt-1 text-sm text-blue-700">
                Get notified about important updates, new content, and marketing insights.
              </p>
              <div className="mt-3 flex space-x-3">
                <button
                  onClick={handleEnableNotifications}
                  disabled={isUpdating}
                  className="bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-blue-700 disabled:opacity-50"
                >
                  {isUpdating ? 'Enabling...' : 'Enable Notifications'}
                </button>
                <button
                  onClick={() => setIsExpanded(false)}
                  className="text-blue-600 text-sm font-medium hover:text-blue-800"
                >
                  Not Now
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 border-b border-red-200">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-red-800">
                Notification Error
              </h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
              <button
                onClick={clearError}
                className="mt-2 text-red-600 text-sm font-medium hover:text-red-800"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Notification Preferences */}
      {isInitialized && (
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Notification Preferences
            </h3>
            <div className="flex items-center">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Enabled
              </span>
            </div>
          </div>

          {prefsLoading ? (
            <div className="space-y-3">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="animate-pulse flex items-center justify-between">
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-6 bg-gray-200 rounded w-12"></div>
                </div>
              ))}
            </div>
          ) : preferences ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Newsletter Updates
                  </label>
                  <p className="text-sm text-gray-500">
                    Get notified about new blog posts and marketing insights
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.preferences?.newsletter || false}
                    onChange={(e) => handlePreferenceChange('newsletter', e.target.checked)}
                    disabled={isUpdating}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Marketing Updates
                  </label>
                  <p className="text-sm text-gray-500">
                    Receive notifications about new features and promotions
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.preferences?.marketing || false}
                    onChange={(e) => handlePreferenceChange('marketing', e.target.checked)}
                    disabled={isUpdating}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Account Updates
                  </label>
                  <p className="text-sm text-gray-500">
                    Important notifications about your account and security
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.preferences?.userUpdates || false}
                    onChange={(e) => handlePreferenceChange('userUpdates', e.target.checked)}
                    disabled={isUpdating}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <p className="text-xs text-gray-500">
                  You can change these preferences at any time. Some notifications may still be sent for important account or security updates.
                </p>
              </div>
            </div>
          ) : (
            <p className="text-sm text-gray-500">
              Unable to load notification preferences.
            </p>
          )}
        </div>
      )}
    </div>
  );
}
