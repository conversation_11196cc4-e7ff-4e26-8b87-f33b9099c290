import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
import { getAuth, Auth } from 'firebase/auth';
import { getFirestore, Firestore } from 'firebase/firestore';
import { getStorage, FirebaseStorage } from 'firebase/storage';
import { getMessaging, Messaging, isSupported } from 'firebase/messaging';
import { firebaseConfig } from './env';

// Initialize Firebase App (singleton pattern)
let app: FirebaseApp;
let auth: Auth;
let db: Firestore;
let storage: FirebaseStorage;
let messaging: Messaging | null = null;

// Initialize Firebase app
function initializeFirebaseApp(): FirebaseApp {
  if (getApps().length === 0) {
    app = initializeApp(firebaseConfig);
  } else {
    app = getApps()[0];
  }
  return app;
}

// Initialize Firebase services
export function initializeFirebase() {
  if (!app) {
    app = initializeFirebaseApp();
  }

  // Initialize Auth
  if (!auth) {
    auth = getAuth(app);
  }

  // Initialize Firestore
  if (!db) {
    db = getFirestore(app);
  }

  // Initialize Storage
  if (!storage) {
    storage = getStorage(app);
  }

  // Initialize Messaging (only in browser and if supported)
  if (typeof window !== 'undefined' && !messaging) {
    isSupported().then((supported) => {
      if (supported) {
        messaging = getMessaging(app);
      }
    });
  }

  return { app, auth, db, storage, messaging };
}

// Export initialized services
const firebase = initializeFirebase();

export const firebaseApp = firebase.app;
export const firebaseAuth = firebase.auth;
export const firebaseDb = firebase.db;
export const firebaseStorage = firebase.storage;
export const firebaseMessaging = firebase.messaging;

// Export Firebase config for reference
export { firebaseConfig };

// Helper function to check if Firebase is initialized
export function isFirebaseInitialized(): boolean {
  return getApps().length > 0;
}

// Helper function to get Firebase app instance
export function getFirebaseApp(): FirebaseApp {
  if (!isFirebaseInitialized()) {
    throw new Error('Firebase is not initialized. Call initializeFirebase() first.');
  }
  return getApps()[0];
}

// Re-export Firebase types for convenience
export type {
  FirebaseApp,
  Auth,
  Firestore,
  FirebaseStorage,
  Messaging,
};

// Re-export Firestore functions for convenience
export {
  doc,
  collection,
  addDoc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  endBefore,
  onSnapshot,
  serverTimestamp,
  increment,
  arrayUnion,
  arrayRemove,
  writeBatch,
  runTransaction,
} from 'firebase/firestore';
