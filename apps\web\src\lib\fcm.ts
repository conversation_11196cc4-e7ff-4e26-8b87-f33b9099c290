/**
 * Firebase Cloud Messaging Client Integration for Web App
 * 
 * Handles FCM token management, notification permissions, and message handling.
 */

import { getMessaging, getToken, onMessage, MessagePayload, Messaging } from 'firebase/messaging';
import { firebaseApp } from './firebase';
import { env } from './env';

// FCM instance
let messaging: Messaging | null = null;

// ========================================
// INITIALIZATION
// ========================================

/**
 * Initialize FCM messaging
 */
export function initializeFCM(): Messaging | null {
  if (typeof window === 'undefined') {
    console.warn('FCM can only be initialized in the browser');
    return null;
  }

  if (!messaging) {
    try {
      messaging = getMessaging(firebaseApp);
      console.log('FCM initialized successfully');
    } catch (error) {
      console.error('Failed to initialize FCM:', error);
      return null;
    }
  }

  return messaging;
}

// ========================================
// PERMISSION MANAGEMENT
// ========================================

/**
 * Request notification permission from user
 */
export async function requestNotificationPermission(): Promise<boolean> {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    console.warn('Notifications not supported in this environment');
    return false;
  }

  try {
    const permission = await Notification.requestPermission();
    console.log('Notification permission:', permission);
    return permission === 'granted';
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
}

/**
 * Check current notification permission status
 */
export function getNotificationPermission(): NotificationPermission {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    return 'default';
  }
  return Notification.permission;
}

/**
 * Check if notifications are supported
 */
export function isNotificationSupported(): boolean {
  return typeof window !== 'undefined' && 'Notification' in window && 'serviceWorker' in navigator;
}

// ========================================
// TOKEN MANAGEMENT
// ========================================

/**
 * Get FCM registration token
 */
export async function getFCMToken(): Promise<string | null> {
  if (!isNotificationSupported()) {
    console.warn('Notifications not supported');
    return null;
  }

  const fcm = initializeFCM();
  if (!fcm) {
    console.error('FCM not initialized');
    return null;
  }

  try {
    // Register service worker
    const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
    console.log('Service worker registered:', registration);

    // Get token
    const token = await getToken(fcm, {
      vapidKey: env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
      serviceWorkerRegistration: registration,
    });

    if (token) {
      console.log('FCM token obtained:', token);
      return token;
    } else {
      console.log('No registration token available');
      return null;
    }
  } catch (error) {
    console.error('Error getting FCM token:', error);
    return null;
  }
}

/**
 * Save FCM token to user preferences
 */
export async function saveFCMToken(userId: string, token: string): Promise<boolean> {
  try {
    const response = await fetch('/api/fcm/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        token,
        platform: 'web',
        userAgent: navigator.userAgent,
      }),
    });

    if (response.ok) {
      console.log('FCM token saved successfully');
      return true;
    } else {
      console.error('Failed to save FCM token:', response.statusText);
      return false;
    }
  } catch (error) {
    console.error('Error saving FCM token:', error);
    return false;
  }
}

/**
 * Remove FCM token from user preferences
 */
export async function removeFCMToken(userId: string, token: string): Promise<boolean> {
  try {
    const response = await fetch('/api/fcm/token', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        token,
      }),
    });

    if (response.ok) {
      console.log('FCM token removed successfully');
      return true;
    } else {
      console.error('Failed to remove FCM token:', response.statusText);
      return false;
    }
  } catch (error) {
    console.error('Error removing FCM token:', error);
    return false;
  }
}

// ========================================
// MESSAGE HANDLING
// ========================================

/**
 * Set up foreground message listener
 */
export function onForegroundMessage(callback: (payload: MessagePayload) => void): (() => void) | null {
  const fcm = initializeFCM();
  if (!fcm) {
    console.error('FCM not initialized');
    return null;
  }

  try {
    const unsubscribe = onMessage(fcm, (payload) => {
      console.log('Foreground message received:', payload);
      callback(payload);
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error setting up foreground message listener:', error);
    return null;
  }
}

/**
 * Show notification in foreground
 */
export function showForegroundNotification(payload: MessagePayload): void {
  if (!isNotificationSupported() || getNotificationPermission() !== 'granted') {
    console.warn('Cannot show notification: permission not granted');
    return;
  }

  const title = payload.notification?.title || 'Encreasl Notification';
  const options: NotificationOptions = {
    body: payload.notification?.body || 'You have a new notification',
    icon: payload.notification?.icon || '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    image: payload.notification?.image,
    tag: 'encreasl-foreground',
    data: payload.data,
    requireInteraction: false,
    silent: false,
  };

  const notification = new Notification(title, options);

  // Handle notification click
  notification.onclick = () => {
    const clickAction = payload.fcmOptions?.link || payload.data?.clickAction || '/';
    window.open(clickAction, '_blank');
    notification.close();
  };

  // Auto-close after 5 seconds
  setTimeout(() => {
    notification.close();
  }, 5000);
}

// ========================================
// TOPIC MANAGEMENT
// ========================================

/**
 * Subscribe to FCM topic
 */
export async function subscribeToTopic(topic: string): Promise<boolean> {
  try {
    const response = await fetch('/api/fcm/topics/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ topic }),
    });

    if (response.ok) {
      console.log(`Subscribed to topic: ${topic}`);
      return true;
    } else {
      console.error(`Failed to subscribe to topic ${topic}:`, response.statusText);
      return false;
    }
  } catch (error) {
    console.error(`Error subscribing to topic ${topic}:`, error);
    return false;
  }
}

/**
 * Unsubscribe from FCM topic
 */
export async function unsubscribeFromTopic(topic: string): Promise<boolean> {
  try {
    const response = await fetch('/api/fcm/topics/unsubscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ topic }),
    });

    if (response.ok) {
      console.log(`Unsubscribed from topic: ${topic}`);
      return true;
    } else {
      console.error(`Failed to unsubscribe from topic ${topic}:`, response.statusText);
      return false;
    }
  } catch (error) {
    console.error(`Error unsubscribing from topic ${topic}:`, error);
    return false;
  }
}

// ========================================
// UTILITY FUNCTIONS
// ========================================

/**
 * Initialize FCM for authenticated user
 */
export async function initializeFCMForUser(userId: string): Promise<string | null> {
  try {
    // Request permission
    const hasPermission = await requestNotificationPermission();
    if (!hasPermission) {
      console.log('Notification permission denied');
      return null;
    }

    // Get token
    const token = await getFCMToken();
    if (!token) {
      console.log('Failed to get FCM token');
      return null;
    }

    // Save token
    const saved = await saveFCMToken(userId, token);
    if (!saved) {
      console.log('Failed to save FCM token');
      return null;
    }

    // Subscribe to default topics
    await subscribeToTopic('general');
    await subscribeToTopic('marketing');

    console.log('FCM initialized successfully for user:', userId);
    return token;
  } catch (error) {
    console.error('Error initializing FCM for user:', error);
    return null;
  }
}

/**
 * Cleanup FCM for user logout
 */
export async function cleanupFCMForUser(userId: string): Promise<void> {
  try {
    const token = localStorage.getItem('fcm_token');
    if (token) {
      await removeFCMToken(userId, token);
      localStorage.removeItem('fcm_token');
    }

    console.log('FCM cleaned up for user:', userId);
  } catch (error) {
    console.error('Error cleaning up FCM for user:', error);
  }
}

/**
 * Check if FCM is properly set up
 */
export function isFCMSetup(): boolean {
  return (
    isNotificationSupported() &&
    getNotificationPermission() === 'granted' &&
    !!localStorage.getItem('fcm_token')
  );
}
