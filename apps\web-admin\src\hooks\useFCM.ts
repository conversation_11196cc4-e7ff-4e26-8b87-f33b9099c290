'use client';

import { useState, useEffect, useCallback } from 'react';
import { MessagePayload } from 'firebase/messaging';
import {
  initializeFCMForAdmin,
  onForegroundMessage,
  subscribeToTopic,
} from '@/lib/fcm';
import { useAuth } from './useAuth';

export interface AdminFCMState {
  isSupported: boolean;
  isPermissionGranted: boolean;
  token: string | null;
  isInitialized: boolean;
  error: string | null;
  lastMessage: MessagePayload | null;
  urgentMessageCount: number;
  unreadMessageCount: number;
}

export interface AdminFCMActions {
  initialize: () => Promise<boolean>;
  subscribeToTopic: (topic: string) => Promise<boolean>;
  markMessageAsRead: (messageId: string) => void;
  clearUrgentMessages: () => void;
  clearError: () => void;
}

export function useAdminFCM(): AdminFCMState & AdminFCMActions {
  const { user, profile } = useAuth();
  const [state, setState] = useState<AdminFCMState>({
    isSupported: false,
    isPermissionGranted: false,
    token: null,
    isInitialized: false,
    error: null,
    lastMessage: null,
    urgentMessageCount: 0,
    unreadMessageCount: 0,
  });

  // Check if FCM is supported and user is admin
  useEffect(() => {
    const checkSupport = () => {
      const supported = typeof window !== 'undefined' &&
                       'Notification' in window &&
                       'serviceWorker' in navigator;

      setState(prev => ({
        ...prev,
        isSupported: supported,
        isPermissionGranted: supported ? globalThis.Notification.permission === 'granted' : false,
      }));
    };

    checkSupport();
  }, []);

  // Initialize FCM when admin user is authenticated
  useEffect(() => {
    if (user && profile?.isAdmin && state.isSupported && !state.isInitialized) {
      initialize();
    }
  }, [user, profile?.isAdmin, state.isSupported, state.isInitialized]);

  // Set up admin foreground message listener
  useEffect(() => {
    if (!state.isInitialized) return;

    const unsubscribe = onForegroundMessage((payload) => {
      console.log('Admin foreground message received:', payload);
      
      const isUrgent = payload.data?.priority === 'urgent';
      const messageId = payload.data?.notificationId || Date.now().toString();
      
      setState(prev => ({
        ...prev,
        lastMessage: payload,
        urgentMessageCount: isUrgent ? prev.urgentMessageCount + 1 : prev.urgentMessageCount,
        unreadMessageCount: prev.unreadMessageCount + 1,
      }));

      // Handle admin-specific message processing
      handleAdminMessage(payload);
      
      // Store message for admin dashboard
      storeAdminMessage(payload, messageId);
    });

    return unsubscribe || undefined;
  }, [state.isInitialized]);

  // Listen for admin notification events from service worker
  useEffect(() => {
    const handleAdminNotification = (event: globalThis.CustomEvent) => {
      const { messageType, data } = event.detail;
      console.log('Admin notification event received:', messageType, data);
      
      // Update dashboard state based on notification type
      updateAdminDashboardState(messageType, data);
    };

    window.addEventListener('adminNotificationReceived', handleAdminNotification as globalThis.EventListener);

    return () => {
      window.removeEventListener('adminNotificationReceived', handleAdminNotification as globalThis.EventListener);
    };
  }, []);

  // Initialize admin FCM
  const initialize = useCallback(async (): Promise<boolean> => {
    if (!user || !profile?.isAdmin || !state.isSupported) {
      setState(prev => ({
        ...prev,
        error: 'Cannot initialize admin FCM: user not authenticated, not admin, or FCM not supported',
      }));
      return false;
    }

    try {
      setState(prev => ({ ...prev, error: null }));
      
      const token = await initializeFCMForAdmin(user.uid);
      
      if (token) {
        setState(prev => ({
          ...prev,
          token,
          isInitialized: true,
          isPermissionGranted: true,
        }));
        
        // Store admin token
        localStorage.setItem('admin_fcm_token', token);
        
        // Load existing unread messages
        await loadUnreadMessages();
        
        return true;
      } else {
        setState(prev => ({
          ...prev,
          error: 'Failed to initialize admin FCM: could not get token',
        }));
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({
        ...prev,
        error: `Admin FCM initialization failed: ${errorMessage}`,
      }));
      return false;
    }
  }, [user, profile?.isAdmin, state.isSupported]);

  // Subscribe to admin topic
  const handleSubscribeToTopic = useCallback(async (topic: string): Promise<boolean> => {
    if (!state.isInitialized) {
      setState(prev => ({
        ...prev,
        error: 'Admin FCM not initialized',
      }));
      return false;
    }

    try {
      const success = await subscribeToTopic(topic);
      if (!success) {
        setState(prev => ({
          ...prev,
          error: `Failed to subscribe to admin topic: ${topic}`,
        }));
      }
      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({
        ...prev,
        error: `Admin topic subscription failed: ${errorMessage}`,
      }));
      return false;
    }
  }, [state.isInitialized]);

  // Mark message as read
  const markMessageAsRead = useCallback((messageId: string) => {
    setState(prev => ({
      ...prev,
      unreadMessageCount: Math.max(0, prev.unreadMessageCount - 1),
    }));

    // Update local storage
    const readMessages = JSON.parse(localStorage.getItem('admin_read_messages') || '[]');
    readMessages.push(messageId);
    localStorage.setItem('admin_read_messages', JSON.stringify(readMessages));
  }, []);

  // Clear urgent messages
  const clearUrgentMessages = useCallback(() => {
    setState(prev => ({
      ...prev,
      urgentMessageCount: 0,
    }));
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Handle admin-specific message processing
  const handleAdminMessage = useCallback((payload: MessagePayload) => {
    const messageType = payload.data?.type;
    const priority = payload.data?.priority || 'normal';

    // Show browser notification for urgent messages
    if (priority === 'urgent' && document.visibilityState !== 'visible') {
      // The service worker will handle background notifications
      return;
    }

    // Show in-app notification for normal priority messages
    if (document.visibilityState === 'visible') {
      showInAppNotification(payload);
    }

    // Update admin counters based on message type
    updateAdminCounters(messageType);
  }, []);

  // Store admin message for dashboard
  const storeAdminMessage = useCallback((payload: MessagePayload, messageId: string) => {
    const adminMessages = JSON.parse(localStorage.getItem('admin_messages') || '[]');
    const message = {
      id: messageId,
      payload,
      timestamp: Date.now(),
      read: false,
    };
    
    adminMessages.unshift(message);
    
    // Keep only last 100 messages
    if (adminMessages.length > 100) {
      adminMessages.splice(100);
    }
    
    localStorage.setItem('admin_messages', JSON.stringify(adminMessages));
  }, []);

  // Load unread messages count
  const loadUnreadMessages = useCallback(async () => {
    try {
      const adminMessages = JSON.parse(localStorage.getItem('admin_messages') || '[]');
      const readMessages = JSON.parse(localStorage.getItem('admin_read_messages') || '[]');
      
      const unreadCount = adminMessages.filter((msg: { id: string }) => !readMessages.includes(msg.id)).length;
      const urgentCount = adminMessages.filter((msg: { id: string; payload: { data?: { priority?: string } } }) =>
        !readMessages.includes(msg.id) && msg.payload.data?.priority === 'urgent'
      ).length;
      
      setState(prev => ({
        ...prev,
        unreadMessageCount: unreadCount,
        urgentMessageCount: urgentCount,
      }));
    } catch (error) {
      console.error('Error loading unread messages:', error);
    }
  }, []);

  // Show in-app notification
  const showInAppNotification = useCallback((payload: MessagePayload) => {
    // Create toast notification element
    const toast = document.createElement('div');
    const isUrgent = payload.data?.priority === 'urgent';
    
    toast.className = `fixed top-4 right-4 max-w-sm w-full ${
      isUrgent ? 'bg-red-500' : 'bg-blue-500'
    } text-white p-4 rounded-lg shadow-lg z-50 transform transition-transform duration-300 translate-x-full`;
    
    toast.innerHTML = `
      <div class="flex items-start">
        <div class="flex-shrink-0">
          ${isUrgent ? '🚨' : '📢'}
        </div>
        <div class="ml-3 flex-1">
          <p class="text-sm font-medium">${payload.notification?.title || 'Admin Notification'}</p>
          <p class="text-sm opacity-90">${payload.notification?.body || ''}</p>
        </div>
        <button class="ml-3 flex-shrink-0 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    `;

    document.body.appendChild(toast);

    // Animate in
    globalThis.setTimeout(() => {
      toast.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after delay
    globalThis.setTimeout(() => {
      toast.classList.add('translate-x-full');
      globalThis.setTimeout(() => {
        if (toast.parentElement) {
          document.body.removeChild(toast);
        }
      }, 300);
    }, isUrgent ? 10000 : 5000); // Urgent messages stay longer

    // Handle click to navigate
    toast.addEventListener('click', () => {
      const clickAction = payload.fcmOptions?.link || payload.data?.clickAction || '/admin/dashboard';
      window.location.href = clickAction;
    });
  }, []);

  // Update admin counters
  const updateAdminCounters = useCallback((messageType: string | undefined) => {
    // Dispatch event for admin dashboard components
    const event = new globalThis.CustomEvent('adminCounterUpdate', {
      detail: { messageType }
    });
    window.dispatchEvent(event);
  }, []);

  // Update admin dashboard state
  const updateAdminDashboardState = useCallback((messageType: string, data: unknown) => {
    // This could trigger dashboard refreshes or real-time updates
    console.log('Updating admin dashboard state:', messageType, data);
  }, []);

  return {
    ...state,
    initialize,
    subscribeToTopic: handleSubscribeToTopic,
    markMessageAsRead,
    clearUrgentMessages,
    clearError,
  };
}

// Define notification type
type AdminNotification = {
  id: string;
  payload?: {
    fcmOptions?: { link?: string };
    data?: {
      clickAction?: string;
      type?: string;
      priority?: string;
    };
    notification?: {
      title?: string;
      body?: string;
    };
  };
  timestamp?: number;
  title?: string;
  body?: string;
};

// Hook for admin notification management
export function useAdminNotifications() {
  const [notifications, setNotifications] = useState<AdminNotification[]>([]);
  const [loading, setLoading] = useState(false);

  const loadNotifications = useCallback(async () => {
    setLoading(true);
    try {
      const adminMessages = JSON.parse(localStorage.getItem('admin_messages') || '[]');
      setNotifications(adminMessages);
    } catch (error) {
      console.error('Error loading admin notifications:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearAllNotifications = useCallback(() => {
    localStorage.removeItem('admin_messages');
    localStorage.removeItem('admin_read_messages');
    setNotifications([]);
  }, []);

  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  return {
    notifications,
    loading,
    loadNotifications,
    clearAllNotifications,
  };
}
