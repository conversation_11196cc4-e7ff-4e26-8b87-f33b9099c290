{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalDependencies": [".env.local", ".env"], "globalEnv": ["NODE_ENV", "VERCEL_ENV", "CI"], "globalPassThroughEnv": ["NEXT_PUBLIC_*", "FIREBASE_*", "ADMIN_*", "API_*"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"], "inputs": ["$TURBO_DEFAULT$", ".env.local", ".env"], "env": ["NEXT_PUBLIC_*", "FIREBASE_*", "API_*"]}, "dev": {"cache": false, "persistent": true, "env": ["NEXT_PUBLIC_*", "FIREBASE_*", "ADMIN_*", "API_*"]}, "dev:turbo": {"cache": false, "persistent": true, "env": ["NEXT_PUBLIC_*", "FIREBASE_*", "ADMIN_*", "API_*"]}, "lint": {"dependsOn": ["^lint"]}, "type-check": {"dependsOn": ["^type-check"]}}}