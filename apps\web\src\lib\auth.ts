import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile,
  User,
  UserCredential,
  sendEmailVerification,
  GoogleAuthProvider,
  signInWithPopup,
  onAuthStateChanged,
  Unsubscribe,
} from 'firebase/auth';
import { firebaseAuth } from './firebase';

// Authentication error types
export interface AuthError {
  code: string;
  message: string;
}

// User profile data
export interface UserProfile {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

// Sign up with email and password
export async function signUp(email: string, password: string, displayName?: string): Promise<UserCredential> {
  try {
    const userCredential = await createUserWithEmailAndPassword(firebaseAuth, email, password);
    
    // Update profile with display name if provided
    if (displayName && userCredential.user) {
      await updateProfile(userCredential.user, { displayName });
    }
    
    // Send email verification
    if (userCredential.user) {
      await sendEmailVerification(userCredential.user);
    }
    
    return userCredential;
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Sign in with email and password
export async function signIn(email: string, password: string): Promise<UserCredential> {
  try {
    return await signInWithEmailAndPassword(firebaseAuth, email, password);
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Sign in with Google
export async function signInWithGoogle(): Promise<UserCredential> {
  try {
    const provider = new GoogleAuthProvider();
    provider.addScope('email');
    provider.addScope('profile');
    
    return await signInWithPopup(firebaseAuth, provider);
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Sign out
export async function signOutUser(): Promise<void> {
  try {
    await signOut(firebaseAuth);
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Reset password
export async function resetPassword(email: string): Promise<void> {
  try {
    await sendPasswordResetEmail(firebaseAuth, email);
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Update user profile
export async function updateUserProfile(user: User, profile: { displayName?: string; photoURL?: string }): Promise<void> {
  try {
    await updateProfile(user, profile);
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Send email verification
export async function sendVerificationEmail(user: User): Promise<void> {
  try {
    await sendEmailVerification(user);
  } catch (error: any) {
    throw {
      code: error.code,
      message: getAuthErrorMessage(error.code),
    } as AuthError;
  }
}

// Get current user
export function getCurrentUser(): User | null {
  return firebaseAuth.currentUser;
}

// Get user profile data
export function getUserProfile(user: User): UserProfile {
  return {
    uid: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    emailVerified: user.emailVerified,
  };
}

// Auth state listener
export function onAuthStateChange(callback: (user: User | null) => void): Unsubscribe {
  return onAuthStateChanged(firebaseAuth, callback);
}

// Get ID token for API calls
export async function getIdToken(forceRefresh = false): Promise<string | null> {
  const user = getCurrentUser();
  if (!user) return null;
  
  try {
    return await user.getIdToken(forceRefresh);
  } catch (error) {
    console.error('Error getting ID token:', error);
    return null;
  }
}

// Check if user is authenticated
export function isAuthenticated(): boolean {
  return getCurrentUser() !== null;
}

// Check if user email is verified
export function isEmailVerified(): boolean {
  const user = getCurrentUser();
  return user ? user.emailVerified : false;
}

// Helper function to get user-friendly error messages
function getAuthErrorMessage(errorCode: string): string {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email address.';
    case 'auth/wrong-password':
      return 'Incorrect password.';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists.';
    case 'auth/weak-password':
      return 'Password should be at least 6 characters.';
    case 'auth/invalid-email':
      return 'Invalid email address.';
    case 'auth/user-disabled':
      return 'This account has been disabled.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your connection.';
    case 'auth/popup-closed-by-user':
      return 'Sign-in popup was closed before completion.';
    case 'auth/cancelled-popup-request':
      return 'Sign-in was cancelled.';
    default:
      return 'An error occurred during authentication.';
  }
}

// Export types
export type { User, UserCredential, Unsubscribe };
