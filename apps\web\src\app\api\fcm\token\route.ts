import { NextRequest, NextResponse } from 'next/server';
import { getAdminDb } from '@/lib/firebase-admin';
import { collection, doc, setDoc, deleteDoc, query, where, getDocs, Timestamp } from 'firebase/firestore';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, token, platform, userAgent } = body;

    // Validate required fields
    if (!userId || !token || !platform) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, token, platform' },
        { status: 400 }
      );
    }

    // Get Firebase Admin Firestore instance
    const db = getAdminDb();

    // Create FCM device record
    const deviceData = {
      userId,
      token,
      platform,
      browser: getBrowserFromUserAgent(userAgent),
      os: getOSFromUserAgent(userAgent),
      device: getDeviceFromUserAgent(userAgent),
      appVersion: '1.0.0',
      isActive: true,
      lastSeen: Timestamp.now(),
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    };

    // Deactivate old tokens for this user and platform
    const oldTokensQuery = query(
      collection(db, 'fcm_devices'),
      where('userId', '==', userId),
      where('platform', '==', platform)
    );
    
    const oldTokensSnapshot = await getDocs(oldTokensQuery);
    const batch = db.batch();
    
    oldTokensSnapshot.docs.forEach(docSnapshot => {
      batch.update(docSnapshot.ref, { 
        isActive: false, 
        updatedAt: Timestamp.now() 
      });
    });

    // Add new token
    const newTokenRef = doc(collection(db, 'fcm_devices'));
    batch.set(newTokenRef, deviceData);

    await batch.commit();

    // Update user notification preferences
    const userPrefsRef = doc(db, 'notification_preferences', userId);
    const userPrefsDoc = await userPrefsRef.get();
    
    if (userPrefsDoc.exists()) {
      const currentPrefs = userPrefsDoc.data();
      const updatedTokens = [...(currentPrefs.fcmTokens || [])];
      
      // Remove old token if exists and add new one
      const tokenIndex = updatedTokens.indexOf(token);
      if (tokenIndex === -1) {
        updatedTokens.push(token);
      }
      
      await userPrefsRef.update({
        fcmTokens: updatedTokens,
        updatedAt: Timestamp.now(),
      });
    } else {
      // Create default preferences if they don't exist
      await userPrefsRef.set({
        userId,
        email: '', // This should be populated from user data
        fcmTokens: [token],
        topics: ['general'],
        preferences: {
          contactForm: false,
          newsletter: true,
          userUpdates: true,
          campaigns: false,
          dailyDigest: false,
          weeklyReport: false,
          systemAlerts: false,
          marketing: true,
        },
        timezone: 'UTC',
        language: 'en',
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '08:00',
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      });
    }

    return NextResponse.json({
      success: true,
      message: 'FCM token saved successfully',
      deviceId: newTokenRef.id,
    });

  } catch (error) {
    console.error('FCM token save error:', error);
    return NextResponse.json(
      { error: 'Failed to save FCM token' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, token } = body;

    if (!userId || !token) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, token' },
        { status: 400 }
      );
    }

    const db = getAdminDb();

    // Find and deactivate the token
    const tokenQuery = query(
      collection(db, 'fcm_devices'),
      where('userId', '==', userId),
      where('token', '==', token)
    );
    
    const tokenSnapshot = await getDocs(tokenQuery);
    const batch = db.batch();
    
    tokenSnapshot.docs.forEach(docSnapshot => {
      batch.update(docSnapshot.ref, { 
        isActive: false, 
        updatedAt: Timestamp.now() 
      });
    });

    await batch.commit();

    // Remove token from user preferences
    const userPrefsRef = doc(db, 'notification_preferences', userId);
    const userPrefsDoc = await userPrefsRef.get();
    
    if (userPrefsDoc.exists()) {
      const currentPrefs = userPrefsDoc.data();
      const updatedTokens = (currentPrefs.fcmTokens || []).filter((t: string) => t !== token);
      
      await userPrefsRef.update({
        fcmTokens: updatedTokens,
        updatedAt: Timestamp.now(),
      });
    }

    return NextResponse.json({
      success: true,
      message: 'FCM token removed successfully',
    });

  } catch (error) {
    console.error('FCM token removal error:', error);
    return NextResponse.json(
      { error: 'Failed to remove FCM token' },
      { status: 500 }
    );
  }
}

// Helper functions to parse user agent
function getBrowserFromUserAgent(userAgent: string = ''): string {
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  if (userAgent.includes('Opera')) return 'Opera';
  return 'Unknown';
}

function getOSFromUserAgent(userAgent: string = ''): string {
  if (userAgent.includes('Windows')) return 'Windows';
  if (userAgent.includes('Mac OS')) return 'macOS';
  if (userAgent.includes('Linux')) return 'Linux';
  if (userAgent.includes('Android')) return 'Android';
  if (userAgent.includes('iOS')) return 'iOS';
  return 'Unknown';
}

function getDeviceFromUserAgent(userAgent: string = ''): string {
  if (userAgent.includes('Mobile')) return 'Mobile';
  if (userAgent.includes('Tablet')) return 'Tablet';
  return 'Desktop';
}
