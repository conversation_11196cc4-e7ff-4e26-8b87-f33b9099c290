import { NextRequest, NextResponse } from 'next/server';
import { getAdminMessaging } from '@/lib/firebase-admin';
import { getAdminDb } from '@/lib/firebase-admin';
import { doc, getDoc, updateDoc, arrayUnion, Timestamp } from 'firebase/firestore';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { topic, userId } = body;

    if (!topic) {
      return NextResponse.json(
        { error: 'Missing required field: topic' },
        { status: 400 }
      );
    }

    // Get user's FCM tokens
    const db = getAdminDb();
    const messaging = getAdminMessaging();

    // If userId is provided, get tokens from user preferences
    let tokens: string[] = [];
    
    if (userId) {
      const userPrefsRef = doc(db, 'notification_preferences', userId);
      const userPrefsDoc = await getDoc(userPrefsRef);
      
      if (userPrefsDoc.exists()) {
        const prefs = userPrefsDoc.data();
        tokens = prefs.fcmTokens || [];
      } else {
        return NextResponse.json(
          { error: 'User preferences not found' },
          { status: 404 }
        );
      }
    } else {
      // If no userId, expect tokens in request body
      tokens = body.tokens || [];
    }

    if (tokens.length === 0) {
      return NextResponse.json(
        { error: 'No FCM tokens found for subscription' },
        { status: 400 }
      );
    }

    // Subscribe tokens to topic
    await messaging.subscribeToTopic(tokens, topic);

    // Update user preferences to include the topic
    if (userId) {
      const userPrefsRef = doc(db, 'notification_preferences', userId);
      await updateDoc(userPrefsRef, {
        topics: arrayUnion(topic),
        updatedAt: Timestamp.now(),
      });
    }

    // Log the subscription
    console.log(`Subscribed ${tokens.length} tokens to topic: ${topic}`);

    return NextResponse.json({
      success: true,
      message: `Successfully subscribed to topic: ${topic}`,
      tokenCount: tokens.length,
    });

  } catch (error) {
    console.error('Topic subscription error:', error);
    return NextResponse.json(
      { error: 'Failed to subscribe to topic' },
      { status: 500 }
    );
  }
}
