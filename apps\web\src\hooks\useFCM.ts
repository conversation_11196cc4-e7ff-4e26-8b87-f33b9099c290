'use client';

import { useState, useEffect, useCallback } from 'react';
import { MessagePayload } from 'firebase/messaging';
import {
  initializeFCMForUser,
  onForegroundMessage,
  showForegroundNotification,
  isFCMSetup,
  subscribeToTopic,
  unsubscribeFromTopic,
  cleanupFCMForUser,
} from '@/lib/fcm';
import { useAuth } from './useAuth';

export interface FCMState {
  isSupported: boolean;
  isPermissionGranted: boolean;
  token: string | null;
  isInitialized: boolean;
  error: string | null;
  lastMessage: MessagePayload | null;
}

export interface FCMActions {
  initialize: () => Promise<boolean>;
  cleanup: () => Promise<void>;
  subscribeToTopic: (topic: string) => Promise<boolean>;
  unsubscribeFromTopic: (topic: string) => Promise<boolean>;
  clearError: () => void;
}

export function useFCM(): FCMState & FCMActions {
  const { user } = useAuth();
  const [state, setState] = useState<FCMState>({
    isSupported: false,
    isPermissionGranted: false,
    token: null,
    isInitialized: false,
    error: null,
    lastMessage: null,
  });

  // Check if FCM is supported
  useEffect(() => {
    const checkSupport = () => {
      const supported = typeof window !== 'undefined' && 
                       'Notification' in window && 
                       'serviceWorker' in navigator;
      
      setState(prev => ({
        ...prev,
        isSupported: supported,
        isPermissionGranted: supported ? Notification.permission === 'granted' : false,
      }));
    };

    checkSupport();
  }, []);

  // Initialize FCM when user is authenticated
  useEffect(() => {
    if (user && state.isSupported && !state.isInitialized) {
      initialize();
    }
  }, [user, state.isSupported, state.isInitialized]);

  // Set up foreground message listener
  useEffect(() => {
    if (!state.isInitialized) return;

    const unsubscribe = onForegroundMessage((payload) => {
      console.log('Foreground message received in hook:', payload);
      
      setState(prev => ({
        ...prev,
        lastMessage: payload,
      }));

      // Show notification if app is in foreground
      if (document.visibilityState === 'visible') {
        showForegroundNotification(payload);
      }
    });

    return unsubscribe || undefined;
  }, [state.isInitialized]);

  // Initialize FCM
  const initialize = useCallback(async (): Promise<boolean> => {
    if (!user || !state.isSupported) {
      setState(prev => ({
        ...prev,
        error: 'Cannot initialize FCM: user not authenticated or FCM not supported',
      }));
      return false;
    }

    try {
      setState(prev => ({ ...prev, error: null }));
      
      const token = await initializeFCMForUser(user.uid);
      
      if (token) {
        setState(prev => ({
          ...prev,
          token,
          isInitialized: true,
          isPermissionGranted: true,
        }));
        
        // Store token in localStorage for quick access
        localStorage.setItem('fcm_token', token);
        
        return true;
      } else {
        setState(prev => ({
          ...prev,
          error: 'Failed to initialize FCM: could not get token',
        }));
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({
        ...prev,
        error: `FCM initialization failed: ${errorMessage}`,
      }));
      return false;
    }
  }, [user, state.isSupported]);

  // Cleanup FCM
  const cleanup = useCallback(async (): Promise<void> => {
    if (!user) return;

    try {
      await cleanupFCMForUser(user.uid);
      
      setState(prev => ({
        ...prev,
        token: null,
        isInitialized: false,
        isPermissionGranted: false,
        lastMessage: null,
        error: null,
      }));
      
      localStorage.removeItem('fcm_token');
    } catch (error) {
      console.error('Error cleaning up FCM:', error);
    }
  }, [user]);

  // Subscribe to topic
  const handleSubscribeToTopic = useCallback(async (topic: string): Promise<boolean> => {
    if (!state.isInitialized) {
      setState(prev => ({
        ...prev,
        error: 'FCM not initialized',
      }));
      return false;
    }

    try {
      const success = await subscribeToTopic(topic);
      if (!success) {
        setState(prev => ({
          ...prev,
          error: `Failed to subscribe to topic: ${topic}`,
        }));
      }
      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({
        ...prev,
        error: `Topic subscription failed: ${errorMessage}`,
      }));
      return false;
    }
  }, [state.isInitialized]);

  // Unsubscribe from topic
  const handleUnsubscribeFromTopic = useCallback(async (topic: string): Promise<boolean> => {
    if (!state.isInitialized) {
      setState(prev => ({
        ...prev,
        error: 'FCM not initialized',
      }));
      return false;
    }

    try {
      const success = await unsubscribeFromTopic(topic);
      if (!success) {
        setState(prev => ({
          ...prev,
          error: `Failed to unsubscribe from topic: ${topic}`,
        }));
      }
      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({
        ...prev,
        error: `Topic unsubscription failed: ${errorMessage}`,
      }));
      return false;
    }
  }, [state.isInitialized]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Don't cleanup FCM on unmount as it should persist across page navigation
    };
  }, []);

  return {
    ...state,
    initialize,
    cleanup,
    subscribeToTopic: handleSubscribeToTopic,
    unsubscribeFromTopic: handleUnsubscribeFromTopic,
    clearError,
  };
}

// Hook for notification preferences
export function useNotificationPreferences() {
  const { user } = useAuth();
  const [preferences, setPreferences] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load preferences
  const loadPreferences = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/notifications/preferences', {
        headers: {
          'Authorization': `Bearer ${await user.getIdToken()}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPreferences(data);
      } else {
        setError('Failed to load notification preferences');
      }
    } catch (err) {
      setError('Error loading notification preferences');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Update preferences
  const updatePreferences = useCallback(async (newPreferences: any) => {
    if (!user) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await user.getIdToken()}`,
        },
        body: JSON.stringify(newPreferences),
      });

      if (response.ok) {
        const data = await response.json();
        setPreferences(data);
        return true;
      } else {
        setError('Failed to update notification preferences');
        return false;
      }
    } catch (err) {
      setError('Error updating notification preferences');
      return false;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Load preferences on mount
  useEffect(() => {
    loadPreferences();
  }, [loadPreferences]);

  return {
    preferences,
    loading,
    error,
    loadPreferences,
    updatePreferences,
  };
}
