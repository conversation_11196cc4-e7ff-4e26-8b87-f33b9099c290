'use client';

import { useState, useEffect } from 'react';
import { User } from 'firebase/auth';
import { onAuthStateChange, getUserProfile, UserProfile } from '@/lib/auth';

export interface AuthState {
  user: User | null;
  profile: UserProfile | null;
  loading: boolean;
  error: string | null;
}

export function useAuth(): AuthState {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const unsubscribe = onAuthStateChange((user) => {
      try {
        setUser(user);
        setProfile(user ? getUserProfile(user) : null);
        setError(null);
      } catch (err) {
        setError('Failed to get user profile');
        console.error('Auth state change error:', err);
      } finally {
        setLoading(false);
      }
    });

    return unsubscribe;
  }, []);

  return { user, profile, loading, error };
}

// Hook for checking if user is authenticated
export function useAuthState(): { isAuthenticated: boolean; loading: boolean } {
  const { user, loading } = useAuth();
  return { isAuthenticated: !!user, loading };
}

// Hook for getting user profile
export function useUserProfile(): { profile: UserProfile | null; loading: boolean } {
  const { profile, loading } = useAuth();
  return { profile, loading };
}
